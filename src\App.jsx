import React, { useState } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
<<<<<<< HEAD
import { Toaster } from 'react-hot-toast';
=======

>>>>>>> 0adb0d5d9650660f140bdf4626640b875a1d946a
import Navbar from "./Components/Navbar.jsx";
import Hero from "./Components/Hero.jsx";
import AnimatedCardsSection from "./Components/AnimatedCardsSection.jsx";
import About from "./Components/About.jsx";
import HowItWorks from "./Components/HowItWorks.jsx";
import Testimonials from "./Components/Testimonials.jsx";
import Footer from "./Components/Footer.jsx";

import Layout from "./Components/Layout.jsx";
import CompanyDashboard from "./Components/company/CompanyDashboard.jsx";
import CreateJob from "./Components/company/Createjob.jsx";
import TestManagement from "./Components/company/TestManagement.jsx";
import Aptitude from "./Components/company/Aptitude.jsx";
import Interview from "./Components/company/Interview.jsx";
import Profile from "./Components/company/Profile.jsx";
<<<<<<< HEAD
// import CompanyLogin from './Components/company/CompanyLogin';
import RegistrationPage from "./pages/RegistationPage.jsx";
import VerifyOtp from "./pages/VerifyOtp.jsx";
import LoginPage from "./pages/LoginPage.jsx";
=======
import Test from "./Components/company/test.jsx";

import AdminDashboard from "./Components/admin/AdminDashboard.jsx";
// Add these imports for admin pages
import AdminUsers from "./Components/admin/AdminUsers.jsx";
import AdminCompanies from "./Components/admin/AdminCompanies.jsx";
import AdminJobPosts from "./Components/admin/AdminJobPosts.jsx";
import AdminSettings from "./Components/admin/AdminSettings.jsx";
import LoginPage from "./pages/LoginPage.jsx"; // 🔥 Unified Login Page

import axios from 'axios';

const API = axios.create({
  baseURL: 'https://resumebuilder-m27v.onrender.com',
});

API.interceptors.request.use((config) => {
  const token = localStorage.getItem('token'); // ✅ pull from storage
  if (token) {
    config.headers.Authorization = `Bearer ${token}`; // ✅ attach
  }
  return config;
});

// Shared context for companies and job posts
export const SharedDataContext = React.createContext();

// 🏠 Landing Page Component
>>>>>>> 0adb0d5d9650660f140bdf4626640b875a1d946a
function LandingPage() {
  return (
    <div className="font-sans text-gray-800 bg-[#f7f8fa]">
      <Navbar />
      <main>
        <section className="min-h-[85vh] flex items-center justify-center bg-white">
          <Hero />
        </section>
        <section className="py-16 px-4 bg-white">
          <div className="max-w-7xl mx-auto bg-white">
            <AnimatedCardsSection />
          </div>
        </section>
        <section className="py-20 px-4 bg-white">
          <div>
            <About />
          </div>
        </section>
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <HowItWorks />
          </div>
        </section>
        <section className="py-20 px-4">
          <div>
            <Testimonials dark={false} />
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}

// 🔧 App Router
function App() {
  const [theme, setTheme] = useState(localStorage.getItem('theme') || 'light');
  // Shared state for companies and job posts, persisted in localStorage
  const [companies, setCompanies] = useState(() => {
    const stored = localStorage.getItem('companies');
    return stored ? JSON.parse(stored) : [];
  });
  const [jobPosts, setJobPosts] = useState(() => {
    const stored = localStorage.getItem('jobPosts');
    return stored ? JSON.parse(stored) : [];
  });

  const addCompany = (company) => setCompanies((prev) => {
    const updated = [company, ...prev]; // Prepend new company
    localStorage.setItem('companies', JSON.stringify(updated));
    return updated;
  });
  const addJobPost = (jobPost) => setJobPosts((prev) => {
    const updated = [jobPost, ...prev]; // Prepend new job post
    localStorage.setItem('jobPosts', JSON.stringify(updated));
    return updated;
  });

  // Keep localStorage in sync if companies/jobPosts are updated elsewhere
  React.useEffect(() => {
    localStorage.setItem('companies', JSON.stringify(companies));
  }, [companies]);
  React.useEffect(() => {
    localStorage.setItem('jobPosts', JSON.stringify(jobPosts));
  }, [jobPosts]);

  React.useEffect(() => {
    localStorage.setItem('theme', theme);
    const root = window.document.documentElement;
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme]);

  return (
<<<<<<< HEAD
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        {/* <Route path="/company-login" element={<CompanyLogin />} /> */}
        <Route path="/register" element={<RegistrationPage />} />
        <Route path="/verify-otp" element={<VerifyOtp />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/" element={<Layout />}>
          <Route path="dashboard" element={<CompanyDashboard />} />
          <Route path="job-create" element={<CreateJob />} />
          <Route path="test-management" element={<TestManagement />} />
          <Route path="aptitude" element={<Aptitude />} />
          <Route path="interview" element={<Interview />} />
          <Route path="profile" element={<Profile />} />
        </Route>
      </Routes>

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
            borderRadius: '10px',
            padding: '16px',
            fontSize: '14px',
            fontWeight: '500',
          },
          success: {
            style: {
              background: '#10B981',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#10B981',
            },
          },
          error: {
            style: {
              background: '#EF4444',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#EF4444',
            },
          },
          loading: {
            style: {
              background: '#3B82F6',
            },
          },
        }}
      />
    </Router>
=======
    <SharedDataContext.Provider value={{ companies, addCompany, jobPosts, addJobPost, setCompanies, setJobPosts, theme, setTheme }}>
      <Router>
        <Routes>
          {/* Landing Page */}
          <Route path="/" element={<LandingPage />} />
          {/* Unified Login */}
          <Route path="/login" element={<LoginPage />} />
          {/* Admin Dashboard with nested routes */}
          <Route path="/admin-dashboard" element={<AdminDashboard />}>
            <Route path="users" element={<AdminUsers />} />
            <Route path="companies" element={<AdminCompanies />} />
            <Route path="job-posts" element={<AdminJobPosts />} />
            <Route path="settings" element={<AdminSettings />} />
          </Route>
          {/* Dashboard route for company users */}
          <Route path="/dashboard" element={<CompanyDashboard />} />
          <Route path="/dashboard/job-create" element={<CreateJob />} />
          <Route path="/dashboard/aptitude" element={<Aptitude />} />
          <Route path="/dashboard/test" element={<Test />} />
          <Route path="/dashboard/interview" element={<Interview />} />
          <Route path="/dashboard/profile" element={<Profile />} />
        </Routes>
      </Router>
    </SharedDataContext.Provider>
>>>>>>> 0adb0d5d9650660f140bdf4626640b875a1d946a
  );
}

export default App;
