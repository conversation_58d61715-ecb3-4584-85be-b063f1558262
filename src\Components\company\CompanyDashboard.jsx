import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import API from '../../api';

const quickLinks = [
  { label: 'Dashboard', to: '/dashboard', icon: '🏠' },
  { label: 'Create Job', to: '/job-create', icon: '➕' },
  { label: 'Aptitude', to: '/aptitude', icon: '📝' },
  { label: 'Interview', to: '/interview', icon: '🎤' },
];

const defaultRoles = [
  'Frontend Developer',
  'Backend Developer',
  'Data Analyst',
  'HR Manager',
  'Product Manager',
];

// New Counter component for animating numbers
const Counter = ({ value }) => {
  const count = useMotionValue(0);
  const rounded = useTransform(count, (latest) => Math.round(latest));

  useEffect(() => {
    const controls = animate(count, value, { duration: 1.5, ease: "easeOut" });
    return controls.stop;
  }, [value]);

  return <motion.div className="text-2xl font-bold">{rounded}</motion.div>;
};

const CompanyDashboard = () => {
  const [stats, setStats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCompanies, setShowCompanies] = useState(false);
  const [showJobPosts, setShowJobPosts] = useState(false);
  const location = useLocation();

  // Animation variants
  const listVariants = {
    visible: { transition: { staggerChildren: 0.12 } },
    hidden: {},
  };
  const cardVariants = {
    hidden: { opacity: 0, y: 24 },
    visible: { opacity: 1, y: 0, transition: { type: 'spring', stiffness: 60 } },
    exit: { opacity: 0, y: 24, transition: { duration: 0.15 } },
  };

  useEffect(() => {
    const initializeAndFetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check authentication first
        if (!user) {
          const currentUser = await fetchCurrentUser();
          if (!currentUser) {
            navigate('/login');
            return;
          }

          if (currentUser.role !== 'company') {
            navigate('/');
            return;
          }
        } else if (user.role !== 'company') {
          navigate('/');
          return;
        }

        // Fetch dashboard data using the company store
        await getDashboardData();

      } catch (err) {
        console.error('❌ Dashboard error:', err);
        setError(err.message || 'Failed to load dashboard');

        // If unauthorized, redirect to login
        if (err.response?.status === 401) {
          navigate('/login');
        }
      } finally {
        setLoading(false);
      }
    };

    initializeAndFetchData();
  }, [user, fetchCurrentUser, navigate, getDashboardData]);

  // Update stats when dashboard data changes
  useEffect(() => {
    if (dashboard) {
      console.log('✅ Dashboard data:', dashboard);
      setStats([
        { label: 'No. of vacancy', value: dashboard.totalJobs || 0 },
        { label: 'No. of job position', value: dashboard.activeJobs || 0 },
        { label: 'No. of response', value: dashboard.applications || 0 },
        { label: 'Active tests', value: dashboard.activeTests || 0 },
      ]);
    }
  }, [dashboard]);

  // Handle company store error
  useEffect(() => {
    if (companyError) {
      setError(companyError);
    }
  }, [companyError]);

  if (loading || companyLoading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-gray-100 to-white">
        <div className="text-2xl font-semibold text-gray-600">Loading dashboard...</div>
      </div>
    );
  }

  if (error || companyError) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-gray-100 to-white">
        <div className="text-center">
          <div className="text-2xl font-semibold text-red-600 mb-4">Error loading dashboard</div>
          <div className="text-gray-600">{error || companyError}</div>
          <button
            onClick={() => {
              setError(null);
              getDashboardData();
            }}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen flex flex-col items-center justify-start bg-gradient-to-br from-gray-50 via-gray-100 to-white font-sans p-0">
      <h1 className="text-4xl md:text-5xl font-extrabold text-center mt-12 mb-10 text-[rgb(35,65,75)] drop-shadow-lg tracking-tight">Company Dashboard</h1>
      <div className="flex justify-center space-x-8 mb-12">
        {stats.map((stat, i) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 + i * 0.25, type: 'spring', stiffness: 80 }}
            whileHover={{ scale: 1.08, y: -16, boxShadow: '0 12px 32px 0 rgba(35,65,75,0.13)' }}
            className="bg-gray-100 border border-gray-300 shadow-lg p-8 w-64 flex flex-col items-center rounded-xl cursor-pointer transition-all duration-300"
          >
            <div className="text-3xl font-bold text-blue-600 mb-2">{stat.value}</div>
            <div className="text-lg text-gray-700">{stat.label}</div>
          </motion.div>
        ))}
      </div>
      {/* Available Jobs/Roles */}
      <div className="w-full max-w-4xl bg-white/60 backdrop-blur-lg rounded-2xl shadow-xl border border-gray-200 px-8 py-8 mb-10 flex flex-col items-center">
        <h2 className="text-2xl font-extrabold text-[rgb(35,65,75)] mb-2 flex items-center gap-2 tracking-tight">
          <svg className="w-6 h-6 text-[rgb(35,65,75)]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M4 12h16" /></svg>
          Available Jobs / Roles
        </h2>
        <div className="text-gray-500 text-base mb-6 text-center">Explore the roles your company is hiring for</div>
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-6">
          {defaultRoles.map((role, idx) => (
            <motion.div
              key={role}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 + idx * 0.18, type: 'spring', stiffness: 80 }}
              whileHover={{ scale: 1.04, boxShadow: '0 6px 24px 0 rgba(35,65,75,0.10)' }}
              className="bg-gradient-to-br from-blue-50/80 via-white/80 to-blue-100/60 rounded-xl px-6 py-6 flex items-center gap-4 shadow group transition-all duration-300 cursor-pointer border border-blue-100 hover:border-blue-300"
            >
              <span className="w-12 h-12 flex items-center justify-center rounded-full bg-[rgb(35,65,75)]/90 text-white text-2xl shadow group-hover:scale-110 transition-transform duration-300">
                <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /></svg>
              </span>
              <span className="text-lg font-bold text-[rgb(35,65,75)] group-hover:text-blue-700 transition-colors duration-200">{role}</span>
            </motion.div>
          ))}
        </div>
      </div>
      {/* Quick Links - Outlined Icon Row */}
      <div className="w-full max-w-4xl bg-white/90 rounded-2xl shadow-lg border border-gray-200 px-8 py-6 mb-12 flex flex-col items-center">
        <h2 className="text-xl font-bold text-[rgb(35,65,75)] mb-6 flex items-center gap-2">
          <svg className="w-6 h-6 text-[rgb(35,65,75)]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M4 12h16M4 6h16M4 18h16" /></svg>
          Quick Links
        </h2>
        <div className="w-full flex flex-row flex-wrap justify-center gap-10">
          {quickLinks.map((link, i) => (
            link.label === 'Interview' ? (
              <motion.div
                key={link.to}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 + i * 0.18, type: 'spring', stiffness: 80 }}
                className="flex flex-col items-center group"
              >
                <a
                  href="https://interviewpage-inky.vercel.app/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-16 h-16 flex items-center justify-center rounded-full border-2 border-[rgb(35,65,75)] text-3xl text-[rgb(35,65,75)] bg-white shadow group-hover:scale-110 group-hover:bg-[rgb(35,65,75)] group-hover:text-white transition-all duration-300 mb-2"
                >
                  {link.icon}
                </a>
                <span className="text-base font-semibold text-[rgb(35,65,75)] group-hover:text-[rgb(35,65,75)]/80 transition-colors duration-200">
                  {link.label}
                </span>
              </motion.div>
            ) : (
              <motion.div
                key={link.to}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 + i * 0.18, type: 'spring', stiffness: 80 }}
                className="flex flex-col items-center group"
              >
                <Link
                  to={link.to}
                  className="w-16 h-16 flex items-center justify-center rounded-full border-2 border-[rgb(35,65,75)] text-3xl text-[rgb(35,65,75)] bg-white shadow group-hover:scale-110 group-hover:bg-[rgb(35,65,75)] group-hover:text-white transition-all duration-300 mb-2"
                >
                  {link.icon}
                </Link>
                <span className="text-base font-semibold text-[rgb(35,65,75)] group-hover:text-[rgb(35,65,75)]/80 transition-colors duration-200">
                  {link.label}
                </span>
              </motion.div>
            )
          ))}
        </div>
      </div>
    </div>
  );
};

export default CompanyDashboard;